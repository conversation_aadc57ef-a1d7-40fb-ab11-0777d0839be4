#!/bin/bash

# CUSTOMER SETTING
# 使用环境变量 source online.sh

# global
export TZ=Asia/Shanghai
export BB_VERSION=V5.0.0
# export DOCKER_DEFAULT_PLATFORM=linux/amd64

# mysql
export BB_MYSQL_HOST=**************
export BB_MYSQL_DBNAME=blackbear
export BB_MYSQL_DBNAME2=blackbear-deploy
export BB_MYSQL_USER=mizhdi
export BB_MYSQL_PASSWD=mzd12345
export BB_MYSQL_PORT=3306

# redis
export BB_REDIS_HOST=**************
export BB_REDIS_PORT=6379
export BB_REDIS_PASSWORD=12345
export BB_REDIS_DB0=0
export BB_WS_REDIS_DB3=3

# 各个项目地址
export BB_PROXY_URL=http://**************:5555
export BB_PROXY_URL2=http://**************:5010
export BB_SCRAPY_URL=http://**************:6800
export BB_TASK_URL=http://**************:5920
export BB_FRONTEND_URL=http://**************:80
# 用ip防止跨域
export BB_API_URL=http://**************:7001
export BB_WEB_URL=http://**************:3000
export BB_ASSETS_URL=https://assets.finevent.top

# 根据oss切换同 ossHost
export BB_IMAGE_URL=http://**************:9000

# crawl
export BB_SCRAPY_PROXY=True
export BB_SCRAPY_DEVELOPMENT=True
#  --------- backend -------------------
# aliyun oss (0)和minio(1)切换
export BB_UseOSS=1
export BB_BB_OssBucketName=blackbear
# 统一oss地址
export BB_OssHost=$BB_IMAGE_URL
# minio
export BB_MinioEndPoint=**************
export BB_MinioPort=9000
export BB_MinioAccessKey=xabALv8xA6q9HBqB6NbT
export BB_MinioSecretKey=bCSy5VQSrluC0KAkKbLyzNHHldFgU7j9dgBFEiT8

# aliyun oss
export BB_OssAccessKeyId=LTAI5t8GvEDoDmDMWEcks6Yo
export BB_OssAccessKeySecret=******************************
export BB_OssBucket=finevent-images
export BB_OssEndpoint=oss-cn-zhangjiakou.aliyuncs.com

# mail
export BB_MailerHost=smtp.qq.com
export BB_MailerPort=465
export BB_MailerAuthUser=<EMAIL>
export BB_MailerAuthPass=xasyimamhyowecgf

# alinode
export BB_AlinodeAppid=82535
export BB_AlinodeSecret=fe8c9ef71d388154332a14700b347d51b4d74534

# wechat
export BB_WechatApiAppId=wx053f8547616de1ed
export BB_WechatApiAppSecret=72b907c5a7e15f2c5c21e9df5760f5dc

# weibo
export BB_PassportWeiboKey=*********
export BB_PassportWeiboSecret=e0febb7ae543ddae0483299759da5515
export BB_WeiboPassportRedirect=http://$BB_FRONTEND_URL/#/loginsuccess

export BB_BingIndexNowKey=********************************
export BB_SystemUserId=fetadmin-xxxx-oooo
export BB_AdminUsername=admin.top

# alisms
export BB_ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5t8GvEDoDmDMWEcks6Yo
export BB_ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************

export BB_ENABLE_NODE_LOG=YES
export BB_AKToolsHost=http://***********:8888
export BB_CACHE_ENABLED=true

# ==================== 新增优化配置 (Local环境) ====================

# JWT配置
export BB_JWT_SECRET=blackbear-local-jwt-secret-key-for-development

# 安全配置
export BB_SECURITY_ENABLED=true
export BB_XSS_FILTER_ENABLED=true
export BB_SQL_INJECTION_FILTER_ENABLED=true
export BB_RATE_LIMIT_ENABLED=true

# 监控配置
export BB_MONITORING_ENABLED=true
export BB_APM_ENABLED=true
export BB_HEALTH_CHECK_ENABLED=true

# 性能配置
export BB_RESPONSE_COMPRESSION_ENABLED=true
export BB_CACHE_OPTIMIZATION_ENABLED=true

# 日志配置
export BB_LOG_LEVEL=DEBUG
export BB_CONSOLE_LOG_LEVEL=DEBUG

# 限流配置（本地环境相对宽松）
export BB_RATE_LIMIT_MAX=2000
export BB_RATE_LIMIT_DURATION=900000

# 中间件配置
export BB_ENHANCED_SECURITY_ENABLED=true
export BB_GLOBAL_ERROR_HANDLER_ENABLED=true
export BB_SMART_RATE_LIMIT_ENABLED=true
export BB_UNIFIED_AUTH_ENABLED=true
export BB_REQUEST_VALIDATOR_ENABLED=true

# 开发环境特殊配置
export NODE_ENV=development
export EGG_SERVER_ENV=local

# 端口配置
export BB_PORT=7001
export BB_HOSTNAME=0.0.0.0

# 数据库连接池优化
export BB_DB_POOL_MAX=15
export BB_DB_POOL_MIN=3
export BB_DB_POOL_ACQUIRE=30000
export BB_DB_POOL_IDLE=10000

# Redis连接优化
export BB_REDIS_CONNECT_TIMEOUT=10000
export BB_REDIS_COMMAND_TIMEOUT=5000
export BB_REDIS_RETRY_DELAY=100
export BB_REDIS_MAX_RETRIES=3

# 文件上传配置
export BB_UPLOAD_FILE_SIZE=20mb
export BB_UPLOAD_MAX_FILES=15

# CORS配置（本地环境允许更多来源）
export BB_CORS_ENABLED=true
export BB_CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000,$BB_FRONTEND_URL,$BB_WEB_URL"

# 会话配置
export BB_SESSION_MAX_AGE=86400000
export BB_SESSION_SECURE=false

# API版本配置
export BB_API_VERSION=v1
export BB_API_DEPRECATION_WARNINGS=true

# 错误处理配置（开发环境显示详细错误）
export BB_ERROR_STACK_TRACE=true
export BB_ERROR_SENSITIVE_FILTER=false

# 验证配置
export BB_VALIDATION_CONVERT_TYPES=true
export BB_VALIDATION_STRIP_UNKNOWN=true

# 缓存配置
export BB_CACHE_DEFAULT_TTL=300
export BB_CACHE_L1_TTL=60
export BB_CACHE_L2_TTL=300
export BB_CACHE_L3_TTL=3600

# 性能监控配置
export BB_PERFORMANCE_MONITORING=true
export BB_SLOW_QUERY_THRESHOLD=1000
export BB_MEMORY_USAGE_WARNING=0.8

# 安全响应头配置
export BB_SECURITY_HEADERS_ENABLED=true
export BB_CONTENT_SECURITY_POLICY_ENABLED=false

# IP白名单（本地环境）
export BB_IP_WHITELIST="127.0.0.1,::1,localhost,************/24"

# 调试配置
export BB_DEBUG_ENABLED=true
export BB_TRACE_ENABLED=true

echo "✅ Blackbear优化配置已加载 (Local环境)"
echo "🔧 环境: local"
echo "🗄️  数据库: $BB_MYSQL_HOST:$BB_MYSQL_PORT/$BB_MYSQL_DBNAME"
echo "📦 Redis: $BB_REDIS_HOST:$BB_REDIS_PORT"
echo "🌐 API地址: $BB_API_URL"
echo "🛡️  安全增强: 已启用"
echo "📊 监控系统: 已启用"
echo "🔍 调试模式: 已启用"