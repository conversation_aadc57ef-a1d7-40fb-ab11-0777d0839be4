'use strict';

/**
 * 统一认证中间件
 * 支持JWT Token、API Key、Session等多种认证方式
 */

module.exports = (options = {}) => {
  const defaultOptions = {
    // 认证方式优先级
    authMethods: ['bearer', 'apikey', 'session'],
    
    // JWT配置
    jwt: {
      secret: process.env.JWT_SECRET,
      algorithms: ['HS256'],
      ignoreExpiration: false,
    },
    
    // API Key配置
    apiKey: {
      header: 'X-API-Key',
      query: 'api_key',
      enabled: true,
    },
    
    // Session配置
    session: {
      enabled: true,
      key: 'user',
    },
    
    // 跳过认证的路径
    skipPaths: [
      '/api/health',
      '/api/health/*',
      '/login',
      '/register',
      '/publickey',
      '/svgcaptcha',
    ],
    
    // 可选认证的路径（认证失败不报错）
    optionalPaths: [
      '/api/web/*',
    ],
    
    ...options,
  };

  return async function unifiedAuth(ctx, next) {
    // 检查是否跳过认证
    if (shouldSkipAuth(ctx.path, defaultOptions.skipPaths)) {
      return await next();
    }

    const isOptional = shouldSkipAuth(ctx.path, defaultOptions.optionalPaths);
    let user = null;
    let authMethod = null;

    // 尝试各种认证方式
    for (const method of defaultOptions.authMethods) {
      try {
        switch (method) {
          case 'bearer':
            user = await authenticateWithJWT(ctx, defaultOptions.jwt);
            authMethod = 'jwt';
            break;
          case 'apikey':
            user = await authenticateWithAPIKey(ctx, defaultOptions.apiKey);
            authMethod = 'apikey';
            break;
          case 'session':
            user = await authenticateWithSession(ctx, defaultOptions.session);
            authMethod = 'session';
            break;
        }
        
        if (user) {
          break;
        }
      } catch (error) {
        ctx.logger.debug(`Authentication failed with ${method}:`, error.message);
      }
    }

    // 设置用户信息
    if (user) {
      ctx.user = user;
      ctx.authMethod = authMethod;
      ctx.isAuthenticated = true;
      
      // 记录认证日志
      ctx.logger.info(`User authenticated: ${user.id} via ${authMethod}`);
    } else {
      ctx.isAuthenticated = false;
      
      // 如果不是可选认证且认证失败，返回401
      if (!isOptional) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: 'Authentication required',
          code: 'UNAUTHORIZED',
        };
        return;
      }
    }

    await next();
  };
};

/**
 * JWT认证
 */
async function authenticateWithJWT(ctx, jwtConfig) {
  const token = extractBearerToken(ctx);
  if (!token) {
    return null;
  }

  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, jwtConfig.secret, {
      algorithms: jwtConfig.algorithms,
      ignoreExpiration: jwtConfig.ignoreExpiration,
    });

    // 从数据库获取用户信息
    const user = await ctx.service.users.findById(decoded.userId || decoded.id);
    if (!user || user.status !== 1) {
      return null;
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions || [],
      tokenPayload: decoded,
    };
  } catch (error) {
    ctx.logger.debug('JWT verification failed:', error.message);
    return null;
  }
}

/**
 * API Key认证
 */
async function authenticateWithAPIKey(ctx, apiKeyConfig) {
  if (!apiKeyConfig.enabled) {
    return null;
  }

  const apiKey = ctx.get(apiKeyConfig.header) || ctx.query[apiKeyConfig.query];
  if (!apiKey) {
    return null;
  }

  try {
    // 从数据库或缓存中验证API Key
    const apiKeyRecord = await ctx.service.apiKeys.findByKey(apiKey);
    if (!apiKeyRecord || !apiKeyRecord.isActive) {
      return null;
    }

    // 更新最后使用时间
    await ctx.service.apiKeys.updateLastUsed(apiKeyRecord.id);

    return {
      id: apiKeyRecord.userId,
      username: apiKeyRecord.user.username,
      email: apiKeyRecord.user.email,
      role: apiKeyRecord.user.role,
      permissions: apiKeyRecord.permissions || [],
      apiKeyId: apiKeyRecord.id,
    };
  } catch (error) {
    ctx.logger.debug('API Key verification failed:', error.message);
    return null;
  }
}

/**
 * Session认证
 */
async function authenticateWithSession(ctx, sessionConfig) {
  if (!sessionConfig.enabled) {
    return null;
  }

  const userId = ctx.session[sessionConfig.key];
  if (!userId) {
    return null;
  }

  try {
    const user = await ctx.service.users.findById(userId);
    if (!user || user.status !== 1) {
      return null;
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions || [],
    };
  } catch (error) {
    ctx.logger.debug('Session verification failed:', error.message);
    return null;
  }
}

/**
 * 提取Bearer Token
 */
function extractBearerToken(ctx) {
  const authorization = ctx.get('Authorization');
  if (!authorization) {
    return null;
  }

  const matches = authorization.match(/^Bearer\s+(.+)$/);
  return matches ? matches[1] : null;
}

/**
 * 检查是否应该跳过认证
 */
function shouldSkipAuth(path, skipPaths) {
  return skipPaths.some(pattern => {
    if (pattern.endsWith('*')) {
      return path.startsWith(pattern.slice(0, -1));
    }
    return path === pattern;
  });
}
