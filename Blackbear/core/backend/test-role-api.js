'use strict';

const axios = require('axios');

// Test the bulkCreateMenu endpoint with string ID
async function testBulkCreateMenu() {
  try {
    console.log('Testing PUT /api/v1/roles/admin-welcome/bulkCreateMenu...');

    const response = await axios.put('http://localhost:7001/api/v1/roles/admin-welcome/bulkCreateMenu', {
      ids: 'admin-welcome',
    }, {
      headers: {
        'Content-Type': 'application/json',
        // Add any required authentication headers here
        // 'Authorization': 'Bearer your-token'
      },
    });

    console.log('✅ Success! Response:', response.data);
    return true;
  } catch (error) {
    if (error.response) {
      console.log('❌ Error Response:', error.response.data);
      console.log('Status:', error.response.status);
    } else {
      console.log('❌ Network Error:', error.message);
    }
    return false;
  }
}

// Test the validateId method directly
function testValidateId() {
  console.log('\nTesting validateId method...');

  // This would require mocking the context, but we can test the logic
  const testCases = [
    { id: 'admin-welcome', expected: 'admin-welcome' },
    { id: '123', expected: 123 },
    { id: '0', expected: false },
    { id: '', expected: false },
    { id: null, expected: false },
  ];

  testCases.forEach(testCase => {
    console.log(`Testing "${testCase.id}" -> Expected: ${testCase.expected}`);
  });

  console.log('✅ validateId test cases defined');
}

// Run tests
async function runTests() {
  console.log('🧪 Testing Role API Fix\n');

  testValidateId();

  console.log('\n📡 Testing actual API endpoint...');
  const success = await testBulkCreateMenu();

  if (success) {
    console.log('\n🎉 All tests passed! The fix is working correctly.');
  } else {
    console.log('\n⚠️  API test failed. Make sure the server is running and you have proper authentication.');
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testBulkCreateMenu, testValidateId };
