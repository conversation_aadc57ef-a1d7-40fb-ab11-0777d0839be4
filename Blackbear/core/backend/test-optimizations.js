#!/usr/bin/env node

/**
 * 优化验证脚本
 * 测试新增的中间件和功能是否正常工作
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Blackbear Backend Optimizations...\n');

// 测试结果收集
const results = {
  passed: 0,
  failed: 0,
  tests: [],
};

/**
 * 测试函数
 */
function test(name, testFn) {
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${name}`);
      results.passed++;
      results.tests.push({ name, status: 'PASS' });
    } else {
      console.log(`❌ ${name}`);
      results.failed++;
      results.tests.push({ name, status: 'FAIL', error: 'Test returned false' });
    }
  } catch (error) {
    console.log(`❌ ${name} - ${error.message}`);
    results.failed++;
    results.tests.push({ name, status: 'FAIL', error: error.message });
  }
}

// 1. 测试中间件文件是否存在
console.log('📁 Testing Middleware Files...');
test('Enhanced Security Middleware exists', () => {
  return fs.existsSync(path.join(__dirname, 'app/middleware/enhanced_security.js'));
});

test('Global Error Handler exists', () => {
  return fs.existsSync(path.join(__dirname, 'app/middleware/global_error_handler.js'));
});

test('Smart Rate Limit exists', () => {
  return fs.existsSync(path.join(__dirname, 'app/middleware/smart_rate_limit.js'));
});

test('Unified Auth exists', () => {
  return fs.existsSync(path.join(__dirname, 'app/middleware/unified_auth.js'));
});

test('Request Validator exists', () => {
  return fs.existsSync(path.join(__dirname, 'app/middleware/request_validator.js'));
});

// 2. 测试配置文件
console.log('\n⚙️  Testing Configuration Files...');
test('Optimized Config exists', () => {
  return fs.existsSync(path.join(__dirname, 'config/config.optimized.js'));
});

test('Environment Config exists', () => {
  return fs.existsSync(path.join(__dirname, '.env.development'));
});

test('Prometheus Config exists', () => {
  return fs.existsSync(path.join(__dirname, 'config/prometheus.yml'));
});

test('Alert Rules exist', () => {
  return fs.existsSync(path.join(__dirname, 'config/alert_rules.yml'));
});

// 3. 测试配置内容
console.log('\n🔧 Testing Configuration Content...');
test('Default config includes enhanced security', () => {
  const configContent = fs.readFileSync(path.join(__dirname, 'config/config.default.js'), 'utf8');
  return configContent.includes('enhancedSecurity');
});

test('Package.json includes new scripts', () => {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  return packageJson.scripts['health-check'] && packageJson.scripts['benchmark'];
});

test('Router includes health endpoints', () => {
  const routerContent = fs.readFileSync(path.join(__dirname, 'app/router.js'), 'utf8');
  return routerContent.includes('/api/health');
});

// 4. 测试依赖包
console.log('\n📦 Testing Dependencies...');
test('Joi dependency installed', () => {
  return fs.existsSync(path.join(__dirname, 'node_modules/joi'));
});

test('JsonWebToken dependency installed', () => {
  return fs.existsSync(path.join(__dirname, 'node_modules/jsonwebtoken'));
});

// 5. 测试文档
console.log('\n📚 Testing Documentation...');
test('API Versioning Strategy exists', () => {
  return fs.existsSync(path.join(__dirname, 'docs/API_VERSIONING_STRATEGY.md'));
});

test('CI/CD Pipeline exists', () => {
  return fs.existsSync(path.join(__dirname, '.github/workflows/ci.yml'));
});

// 6. 测试中间件代码质量
console.log('\n🔍 Testing Code Quality...');
test('Enhanced Security has proper exports', () => {
  const content = fs.readFileSync(path.join(__dirname, 'app/middleware/enhanced_security.js'), 'utf8');
  return content.includes('module.exports') && content.includes('function enhancedSecurity');
});

test('Request Validator has Joi schemas', () => {
  const content = fs.readFileSync(path.join(__dirname, 'app/middleware/request_validator.js'), 'utf8');
  return content.includes('Joi') && content.includes('schemas');
});

test('Global Error Handler has error types', () => {
  const content = fs.readFileSync(path.join(__dirname, 'app/middleware/global_error_handler.js'), 'utf8');
  return content.includes('errorTypes') && content.includes('ValidationError');
});

// 7. 测试环境变量
console.log('\n🌍 Testing Environment Variables...');
test('Development env has required vars', () => {
  const envContent = fs.readFileSync(path.join(__dirname, '.env.development'), 'utf8');
  return envContent.includes('BB_MYSQL_HOST') && 
         envContent.includes('BB_REDIS_HOST') && 
         envContent.includes('BB_SystemUserId');
});

// 8. 测试启动脚本
console.log('\n🚀 Testing Startup Scripts...');
test('Development startup script exists', () => {
  return fs.existsSync(path.join(__dirname, 'start-dev.sh'));
});

test('Startup script is executable', () => {
  const stats = fs.statSync(path.join(__dirname, 'start-dev.sh'));
  return (stats.mode & parseInt('111', 8)) !== 0; // 检查执行权限
});

// 9. 测试中间件集成
console.log('\n🔗 Testing Middleware Integration...');
test('Middleware index file exists', () => {
  return fs.existsSync(path.join(__dirname, 'app/middleware/index.js'));
});

test('Config references new middleware', () => {
  const configContent = fs.readFileSync(path.join(__dirname, 'config/config.default.js'), 'utf8');
  return configContent.includes('enhancedSecurity') && 
         configContent.includes('apmMonitor') && 
         configContent.includes('cacheManager');
});

// 10. 测试安全配置
console.log('\n🛡️  Testing Security Configuration...');
test('Enhanced security has XSS protection', () => {
  const content = fs.readFileSync(path.join(__dirname, 'app/middleware/enhanced_security.js'), 'utf8');
  return content.includes('enableXssFilter') && content.includes('sanitizeString');
});

test('Enhanced security has rate limiting', () => {
  const content = fs.readFileSync(path.join(__dirname, 'app/middleware/enhanced_security.js'), 'utf8');
  return content.includes('rateLimit') && content.includes('checkRateLimit');
});

// 输出测试结果
console.log('\n' + '='.repeat(50));
console.log('📊 Test Results Summary:');
console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

if (results.failed > 0) {
  console.log('\n❌ Failed Tests:');
  results.tests
    .filter(test => test.status === 'FAIL')
    .forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
}

console.log('\n🎯 Optimization Status:');
if (results.failed === 0) {
  console.log('🎉 All optimizations are properly implemented!');
  console.log('✨ The backend is ready for enhanced performance and security.');
} else if (results.failed <= 3) {
  console.log('⚠️  Most optimizations are working, but some issues need attention.');
  console.log('🔧 Please review the failed tests above.');
} else {
  console.log('🚨 Several optimizations need attention.');
  console.log('🛠️  Please review and fix the issues before proceeding.');
}

console.log('\n📋 Next Steps:');
console.log('1. Fix any failed tests');
console.log('2. Start the application with: ./start-dev.sh');
console.log('3. Test health endpoints: curl http://localhost:7001/api/health');
console.log('4. Run performance benchmarks: npm run benchmark');
console.log('5. Monitor application metrics');

// 退出码
process.exit(results.failed > 0 ? 1 : 0);
