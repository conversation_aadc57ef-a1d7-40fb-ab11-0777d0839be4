'use strict';

// 模拟Egg.js的上下文
class MockContext {
  constructor() {
    this.logger = {
      info: (message, ...args) => console.log(`[INFO] ${message}`, ...args),
      warn: (message, ...args) => console.log(`[WARN] ${message}`, ...args),
      error: (message, ...args) => console.log(`[ERROR] ${message}`, ...args),
    };

    this.service = {
      weibo: {
        send_text: async content => {
          console.log(`[MOCK] 微博发送内容: ${content}`);
          return { text: '发送成功', weiboId: 'mock_weibo_id' };
        },
      },
      posts: {
        getRandomSaying: async () => {
          console.log('[MOCK] 获取随机语录');
          return {
            id: 1,
            content: '这是一条测试语录，用于验证微博发布功能是否正常工作。',
            category: 'saying',
            status: 'published',
          };
        },
      },
      calendars: {
        find: async params => {
          console.log('[MOCK] 获取日历数据:', params);
          // 模拟有日历事件的情况
          return {
            calendarData: [{
              calendarEvents: [
                {
                  event: {
                    content: '美联储议息会议',
                    related: 2,
                  },
                },
                {
                  event: {
                    content: '中国GDP数据公布',
                    related: 1,
                  },
                },
              ],
            }],
          };
        },
      },
      configurations: {
        getConfig: async configName => {
          console.log(`[MOCK] 获取配置: ${configName}`);
          return {
            scheduleWB: true, // 启用微博发布
          };
        },
      },
    };

    this.app = {
      config: {
        env: 'test',
      },
    };
  }
}

// 测试微博日历发布任务
async function testWeiboCalTask() {
  console.log('=== 开始测试微博日历发布任务 ===\n');

  try {
    // 创建模拟上下文
    const ctx = new MockContext();

    // 导入weibo_cal.js模块
    const weiboCalModule = require('./app/schedule/weibo_cal.js');

    console.log('✅ 模块导入成功');
    console.log(`定时配置: ${JSON.stringify(weiboCalModule.schedule)}`);
    console.log(`任务函数: ${typeof weiboCalModule.task}`);
    console.log('');

    // 测试任务执行
    console.log('--- 测试任务执行 ---');
    const result = await weiboCalModule.task(ctx);
    console.log('✅ 任务执行成功');
    console.log('执行结果:', result);
    console.log('');

    console.log('=== 测试通过 ===');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testWeiboCalTask().catch(console.error);
}

module.exports = { testWeiboCalTask };
