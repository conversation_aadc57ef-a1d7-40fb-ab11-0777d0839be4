#!/usr/bin/env node

/**
 * Redis连接测试脚本
 * 用于诊断和解决Redis连接问题
 */

const Redis = require('ioredis');

// 从环境变量获取Redis配置
const redisConfig = {
  host: process.env.BB_REDIS_HOST || '127.0.0.1',
  port: parseInt(process.env.BB_REDIS_PORT) || 6379,
  password: process.env.BB_REDIS_PASSWORD || '',
  db: parseInt(process.env.BB_REDIS_DB0) || 0,
  connectTimeout: 30000,
  commandTimeout: 10000,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
  lazyConnect: false,
  keepAlive: 30000,
  family: 4,
};

console.log('🔍 Redis连接测试开始...\n');

console.log('📋 Redis配置信息:');
console.log(`  Host: ${redisConfig.host}`);
console.log(`  Port: ${redisConfig.port}`);
console.log(`  Password: ${redisConfig.password ? '***' : '(无密码)'}`);
console.log(`  Database: ${redisConfig.db}`);
console.log(`  Connect Timeout: ${redisConfig.connectTimeout}ms`);
console.log(`  Command Timeout: ${redisConfig.commandTimeout}ms\n`);

// 创建Redis连接
const redis = new Redis(redisConfig);

// 连接事件监听
redis.on('connect', () => {
  console.log('✅ Redis连接建立成功');
});

redis.on('ready', () => {
  console.log('✅ Redis就绪，可以执行命令');
  testRedisOperations();
});

redis.on('error', (error) => {
  console.error('❌ Redis连接错误:', error.message);
  process.exit(1);
});

redis.on('close', () => {
  console.log('🔌 Redis连接已关闭');
});

redis.on('reconnecting', (delay) => {
  console.log(`🔄 Redis重连中... (延迟: ${delay}ms)`);
});

// 测试Redis操作
async function testRedisOperations() {
  try {
    console.log('\n🧪 开始Redis操作测试...');
    
    // 1. PING测试
    console.log('1. 测试PING命令...');
    const pong = await redis.ping();
    console.log(`   结果: ${pong}`);
    
    // 2. 设置和获取测试
    console.log('2. 测试SET/GET命令...');
    const testKey = 'blackbear:test:connection';
    const testValue = `test-${Date.now()}`;
    
    await redis.set(testKey, testValue, 'EX', 60); // 60秒过期
    const getValue = await redis.get(testKey);
    console.log(`   SET: ${testKey} = ${testValue}`);
    console.log(`   GET: ${testKey} = ${getValue}`);
    
    if (getValue === testValue) {
      console.log('   ✅ SET/GET测试通过');
    } else {
      console.log('   ❌ SET/GET测试失败');
    }
    
    // 3. 数据库信息测试
    console.log('3. 测试INFO命令...');
    const info = await redis.info('server');
    const lines = info.split('\r\n');
    const serverInfo = {};
    
    lines.forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        serverInfo[key] = value;
      }
    });
    
    console.log(`   Redis版本: ${serverInfo.redis_version || '未知'}`);
    console.log(`   运行模式: ${serverInfo.redis_mode || '未知'}`);
    console.log(`   运行时间: ${serverInfo.uptime_in_seconds || '未知'}秒`);
    
    // 4. 连接数测试
    console.log('4. 测试CLIENT LIST命令...');
    const clients = await redis.client('list');
    const clientCount = clients.split('\n').filter(line => line.trim()).length;
    console.log(`   当前连接数: ${clientCount}`);
    
    // 5. 内存使用测试
    console.log('5. 测试MEMORY USAGE命令...');
    try {
      const memoryUsage = await redis.memory('usage', testKey);
      console.log(`   测试键内存使用: ${memoryUsage} bytes`);
    } catch (error) {
      console.log('   内存使用命令不支持 (Redis版本可能较低)');
    }
    
    // 6. 清理测试数据
    console.log('6. 清理测试数据...');
    await redis.del(testKey);
    console.log('   ✅ 测试数据已清理');
    
    console.log('\n🎉 所有Redis测试通过！');
    console.log('✅ Redis连接配置正确，可以正常使用');
    
  } catch (error) {
    console.error('\n❌ Redis操作测试失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    // 关闭连接
    redis.disconnect();
    console.log('\n🔌 Redis连接已断开');
    process.exit(0);
  }
}

// 超时处理
setTimeout(() => {
  console.error('\n⏰ Redis连接测试超时 (30秒)');
  console.error('可能的原因:');
  console.error('  1. Redis服务未启动');
  console.error('  2. 网络连接问题');
  console.error('  3. 防火墙阻止连接');
  console.error('  4. Redis配置错误');
  
  redis.disconnect();
  process.exit(1);
}, 30000);

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n\n👋 用户中断，正在关闭Redis连接...');
  redis.disconnect();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n🛑 进程终止，正在关闭Redis连接...');
  redis.disconnect();
  process.exit(0);
});
