'use strict';

/**
 * 优化后的默认配置
 * 整合三个阶段的所有优化配置
 */

module.exports = appInfo => {
  const config = exports = {};

  // 使用环境变量或默认值
  config.keys = process.env.EGG_KEYS || `${appInfo.name}_1570610257068_8645`;

  // ==================== 优化的中间件配置 ====================
  config.middleware = [
    // 1. 安全相关中间件（最高优先级）
    'securityFilter',
    'advancedRateLimit',
    
    // 2. 监控和追踪中间件
    'apmMonitor',
    
    // 3. 缓存管理中间件
    'cacheManager',
    
    // 4. 响应优化中间件
    'responseOptimizer',
    
    // 5. 原有中间件
    'errorHandler',
    'userRequired',
    'cacheClear',
  ];

  // ==================== 安全配置优化 ====================
  config.security = {
    csrf: {
      enable: false, // API项目通常禁用CSRF
    },
    domainWhiteList: (process.env.DOMAIN_WHITELIST || '').split(',').filter(Boolean),
    methodnoallow: {
      enable: true,
    },
  };

  // ==================== 数据库配置优化 ====================
  config.sequelize = {
    dialect: 'mysql',
    host: process.env.DB_HOST || '127.0.0.1',
    port: parseInt(process.env.DB_PORT) || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'blackbear',
    define: {
      timestamps: true,
      paranoid: true,
      freezeTableName: true,
      underscored: false,
    },
    pool: {
      max: parseInt(process.env.DB_POOL_MAX) || 20,
      min: parseInt(process.env.DB_POOL_MIN) || 5,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE) || 10000,
    },
    logging: process.env.NODE_ENV === 'production' ? false : console.log,
    benchmark: true, // 启用查询性能监控
  };

  // ==================== Redis配置优化 ====================
  config.redis = {
    client: {
      host: process.env.REDIS_HOST || '127.0.0.1',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD || '',
      db: parseInt(process.env.REDIS_DB) || 0,
      connectTimeout: 10000,
      commandTimeout: 5000,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    },
  };

  // ==================== CORS配置优化 ====================
  config.cors = {
    origin: process.env.NODE_ENV === 'production' 
      ? (process.env.CORS_ORIGINS || '').split(',').filter(Boolean)
      : '*',
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS',
    allowHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'X-Requested-With',
      'X-Trace-Id',
      'X-Span-Id',
      'X-API-Key',
    ],
    credentials: true,
    maxAge: 86400, // 24小时
  };

  // ==================== 会话配置优化 ====================
  config.session = {
    key: 'SESSION_BLACKBEAR',
    maxAge: parseInt(process.env.SESSION_MAX_AGE) || 24 * 3600 * 1000, // 24小时
    httpOnly: true,
    encrypt: true,
    signed: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
  };

  // ==================== JWT配置 ====================
  config.jwt = {
    secret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    algorithm: 'HS256',
  };

  // ==================== 文件上传配置优化 ====================
  config.multipart = {
    mode: 'file',
    autoFields: true,
    defaultCharset: 'utf8',
    fieldNameSize: 100,
    fieldSize: '100kb',
    fields: 10,
    fileSize: process.env.UPLOAD_FILE_SIZE || '10mb',
    files: 10,
    fileExtensions: [
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', // 图片
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', // 文档
      '.txt', '.csv', '.json', '.xml', // 文本
      '.zip', '.rar', '.7z', // 压缩包
    ],
    whitelist: [
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.json', '.xml',
      '.zip', '.rar', '.7z',
    ],
  };

  // ==================== 日志配置优化 ====================
  config.logger = {
    level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'INFO' : 'DEBUG'),
    consoleLevel: process.env.CONSOLE_LOG_LEVEL || 'INFO',
    outputJSON: process.env.NODE_ENV === 'production',
    buffer: process.env.NODE_ENV === 'production',
    appLogName: `${appInfo.name}-web.log`,
    coreLogName: 'egg-web.log',
    agentLogName: 'egg-agent.log',
    errorLogName: 'common-error.log',
  };

  // ==================== 集群配置优化 ====================
  config.cluster = {
    listen: {
      port: parseInt(process.env.PORT) || 7001,
      hostname: process.env.HOSTNAME || '0.0.0.0',
    },
  };

  // ==================== 静态资源配置优化 ====================
  config.static = {
    prefix: '/public/',
    dir: path.join(appInfo.baseDir, 'app/public'),
    dynamic: true,
    preload: false,
    buffer: process.env.NODE_ENV === 'production',
    maxFiles: 1000,
    maxAge: process.env.NODE_ENV === 'production' ? 31536000 : 0, // 生产环境1年缓存
  };

  // ==================== 参数验证配置 ====================
  config.validate = {
    convert: true,
    validateRoot: false,
  };

  // ==================== Socket.IO配置优化 ====================
  config.io = {
    init: {
      wsEngine: 'ws',
      pingTimeout: 60000,
      pingInterval: 25000,
      transports: ['websocket', 'polling'],
    },
    namespace: {
      '/': {
        connectionMiddleware: ['auth'],
        packetMiddleware: ['filter'],
      },
    },
    redis: {
      host: process.env.REDIS_HOST || '127.0.0.1',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      auth_pass: process.env.REDIS_PASSWORD || '',
      db: parseInt(process.env.REDIS_DB) || 1,
    },
  };

  // ==================== 监控配置 ====================
  config.monitoring = {
    enabled: process.env.MONITORING_ENABLED !== 'false',
    endpoint: process.env.MONITORING_ENDPOINT || '/metrics',
    collectDefaultMetrics: true,
    timeout: 5000,
  };

  // ==================== 健康检查配置 ====================
  config.health = {
    enabled: true,
    endpoint: '/api/health',
    checks: {
      database: true,
      redis: true,
      memory: true,
      disk: true,
    },
    thresholds: {
      memory: 0.9, // 90%
      disk: 0.9,   // 90%
      responseTime: 1000, // 1秒
    },
  };

  // ==================== 缓存配置 ====================
  config.cache = {
    default: 'redis',
    stores: {
      redis: {
        driver: 'redis',
        host: process.env.REDIS_HOST || '127.0.0.1',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD || '',
        db: parseInt(process.env.REDIS_CACHE_DB) || 2,
      },
    },
  };

  // ==================== 限流配置 ====================
  config.rateLimit = {
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100,
    duration: parseInt(process.env.RATE_LIMIT_DURATION) || 60000, // 1分钟
    errorMessage: 'Rate limit exceeded',
    id: ctx => ctx.ip,
    headers: {
      remaining: 'Rate-Limit-Remaining',
      reset: 'Rate-Limit-Reset',
      total: 'Rate-Limit-Total',
    },
  };

  // ==================== 开发环境特殊配置 ====================
  if (process.env.NODE_ENV === 'development') {
    config.development = {
      watchDirs: ['app', 'config', 'lib'],
      ignoreDirs: ['app/public', 'app/views', 'logs'],
      fastReady: true,
      reloadOnDebug: true,
    };
  }

  return config;
};

const path = require('path');
